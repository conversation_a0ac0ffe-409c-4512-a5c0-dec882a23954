import * as RadixProgress from '@radix-ui/react-progress';
import { mergeClasses } from '@/utils/tailwind';

interface ProgressProps {
  percentage: number;
  color?: string;
  height?: string;
  className?: string;
}

export const Progress = ({
  percentage,
  color = '#518EF8',
  height = 'h-2',
  className,
}: ProgressProps) => {
  return (
    <RadixProgress.Root
      className={mergeClasses(
        'relative w-full overflow-hidden rounded-full bg-gray-200 shadow-inner',
        height,
        className,
      )}
      value={percentage}
    >
      <RadixProgress.Indicator
        className="h-full rounded-full shadow-sm transition-transform duration-500 ease-out"
        style={{
          backgroundColor: color,
          transform: `translateX(-${100 - percentage}%)`,
          boxShadow:
            'inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(255, 255, 255, 0.8)',
        }}
      />
    </RadixProgress.Root>
  );
};

import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { Modal } from '@/components';
import styles from './VendorConnectModal.module.css';
import { MODAL_NAME } from '@/constants';
import { Logo } from '@/libs/ui/Logo/Logo';
import { Button } from '@/libs/ui/Button/Button';
import { Progress } from './Progress';
import amazonIconUrl from './amazon-icon.svg';
import { StepThree } from './StepThree/StepThree';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export const VendorConnectModal = () => {
  const { modalOption } = useModalStore();
  const { vendor } = modalOption as VendorConnectModalOptions;

  if (!vendor) {
    return null;
  }

  return (
    <Modal
      name={MODAL_NAME.VENDOR_CONNECT}
      size="491px"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
    >
      <div className="relative top-[-1.5rem] w-full">
        <div className="flex w-full flex-col items-center">
          <div className="flex">
            <div className="mb-4 rounded border border-black/[0.09] bg-white p-3">
              <Logo type="emblem" className="w-12" />
            </div>
            <span className="mx-4 mt-4 text-3xl">+</span>
            <div className="mb-4 flex items-center justify-center rounded border border-black/[0.09] bg-white p-3">
              <img src={amazonIconUrl} alt="Logo" width="100%" />
            </div>
          </div>
          <p className="mb-2 text-xl font-semibold">Connect with your vendor</p>
          <p className="px-2 text-center text-sm text-black/70">
            Highfive & Amazon Business have a special offer of{' '}
            <span className="text-black">50% Amazon Business Prime</span> AND
            special discounted product pricing! Follow the steps below to redeem
            and connect.
          </p>
          <div className="mt-4 mb-5 flex w-full flex-col items-center rounded-lg border border-black/[0.04] bg-[#F2F8FC] p-4 pb-10">
            <div className="mb-1 flex w-full justify-between">
              <span className="text-sm font-medium">Connect your Account</span>
              <span className="text-xs text-black/70">STEP 3/3</span>
            </div>
            <Progress percentage={100} className="mb-10" />
            <StepThree />
          </div>
          <span className="mb-2 text-[16px]">
            Need help?{' '}
            <Button variant="unstyled">
              <span className="underline">Watch our Tutorial</span>
            </Button>
          </span>
        </div>
      </div>
    </Modal>
  );
};
